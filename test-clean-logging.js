// Test the clean logging implementation
import fs from 'fs';

// Copy the clean calculation function
function calculate90DayAverage(priceHistory, scraperId, enableLogging = false) {
  if (!priceHistory || priceHistory.length === 0) {
    return null;
  }

  // Calculate the date 90 days ago
  const ninetyDaysAgo = new Date();
  ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

  // Filter price history to only include entries from the last 90 days
  // and only include entries with valid prices
  const validRecentHistory = priceHistory.filter(entry => {
    if (!entry || !entry.timestamp || !entry.price) {
      return false;
    }
    
    const entryDate = new Date(entry.timestamp);
    if (isNaN(entryDate.getTime())) {
      return false;
    }
    
    const price = parseFloat(entry.price);
    if (isNaN(price) || price <= 0) {
      return false;
    }
    
    return entryDate >= ninetyDaysAgo;
  });

  if (validRecentHistory.length === 0) {
    return null;
  }

  // Calculate the average price
  let totalPrice = 0;
  
  for (const entry of validRecentHistory) {
    totalPrice += parseFloat(entry.price);
  }

  const averagePrice = totalPrice / validRecentHistory.length;
  const result = averagePrice.toFixed(2);
  
  // Only log if explicitly enabled and scraper ID is provided
  if (enableLogging && scraperId) {
    console.log(`[AVERAGE-CALC] Scraper ${scraperId}: Calculated 90-day average ${result} from ${validRecentHistory.length} price entries`);
  }
  
  // Return the average price rounded to 2 decimal places
  return result;
}

// Read the current database
const dbData = JSON.parse(fs.readFileSync('db.json', 'utf8'));

console.log('=== TESTING CLEAN LOGGING ===\n');

// Test without logging (simulating read operations)
const thinkpadId = '1f417294-782a-47d1-a7ea-e63cf7fbd300';
const thinkpadHistory = dbData.priceHistory.filter(h => h.scraperId === thinkpadId);

console.log('1. Testing without logging (read operation):');
const result1 = calculate90DayAverage(thinkpadHistory, thinkpadId, false);
console.log(`   Result: ${result1} (no logs should appear above this line)\n`);

console.log('2. Testing with logging enabled (update operation):');
const result2 = calculate90DayAverage(thinkpadHistory, thinkpadId, true);
console.log(`   Result: ${result2}\n`);

console.log('3. Testing different scraper with logging:');
const asusId = 'ebd56f46-b0ea-40c6-ba79-57f940eb24d5';
const asusHistory = dbData.priceHistory.filter(h => h.scraperId === asusId);
const result3 = calculate90DayAverage(asusHistory, asusId, true);
console.log(`   Result: ${result3}\n`);

console.log('✅ Clean logging test complete!');
