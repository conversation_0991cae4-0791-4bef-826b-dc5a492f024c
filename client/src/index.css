@tailwind base;
@tailwind components;
@tailwind utilities;

/* LIGHT MODE */
:root {
  --button-outline: rgba(0,0,0, .10);
  --badge-outline: rgba(0,0,0, .05);

  /* Automatic computation of border around primary / danger buttons */
  --opaque-button-border-intensity: -8; /* In terms of percentages */

  /* Backgrounds applied on top of other backgrounds when hovered/active */
  --elevate-1: rgba(0,0,0, .03);
  --elevate-2: rgba(0,0,0, .08);

  --background: #090909;
  --foreground: #ebebeb;
  --border: #2b2b2b;
  --card: #101011;
  --card-foreground: hsl(0, 0%, 100%);
  --card-border: hsl(45, 15%, 80%);
  --sidebar: hsl(45, 25%, 97%);
  --sidebar-foreground: hsl(20, 14%, 17%);
  --sidebar-border: hsl(45, 15%, 80%);
  --sidebar-primary: hsl(9, 75%, 61%);
  --sidebar-primary-foreground: hsl(0, 0%, 100%);
  --sidebar-accent: hsl(25, 45%, 80%);
  --sidebar-accent-foreground: hsl(20, 14%, 17%);
  --sidebar-ring: hsl(9, 75%, 61%);
  --popover: hsl(45, 25%, 97%);
  --popover-foreground: hsl(0, 0%, 100%);
  --popover-border: hsl(45, 15%, 80%);
  --primary: #f5f5f5;
  --primary-foreground: #0d0d0d;
  --secondary: #ebebeb;
  --secondary-foreground: #0d0d0d;
  --muted: #404040;
  --muted-foreground: #ababab;
  --accent: hsl(0, 0%, 92.16%);
  --accent-foreground: hsl(0, 0%, 0%);
  --destructive: hsl(356.3033, 90.5579%, 54.3137%);
  --destructive-foreground: #ffffff;
  --input: #2b2b2b;
  --ring: hsl(221.74, 13.14%, 34.31%);
  --chart-1: hsl(203.8863, 88.2845%, 53.1373%);
  --chart-2: hsl(159.7826, 100%, 36.0784%);
  --chart-3: hsl(42.0290, 92.8251%, 56.2745%);
  --chart-4: hsl(147.1429, 78.5047%, 41.9608%);
  --chart-5: hsl(341.4894, 75.2000%, 50.9804%);

  --font-sans: 'Inter', sans-serif;
  --font-serif: 'Inter', sans-serif;
  --font-mono: Menlo, monospace;
  --radius: 0.4rem; /* 8px */
  --shadow-2xs: 0px 2px 0px 0px hsl(9, 75%, 61% / 0.00);
  --shadow-xs: 0px 2px 0px 0px hsl(9, 75%, 61% / 0.00);
  --shadow-sm: 0px 2px 0px 0px hsl(9, 75%, 61% / 0.00), 0px 1px 2px -1px hsl(9, 75%, 61% / 0.00);
  --shadow: 0px 2px 0px 0px hsl(9, 75%, 61% / 0.00), 0px 1px 2px -1px hsl(9, 75%, 61% / 0.00);
  --shadow-md: 0px 2px 0px 0px hsl(9, 75%, 61% / 0.00), 0px 2px 4px -1px hsl(9, 75%, 61% / 0.00);
  --shadow-lg: 0px 2px 0px 0px hsl(9, 75%, 61% / 0.00), 0px 4px 6px -1px hsl(9, 75%, 61% / 0.00);
  --shadow-xl: 0px 2px 0px 0px hsl(9, 75%, 61% / 0.00), 0px 8px 10px -1px hsl(9, 75%, 61% / 0.00);
  --shadow-2xl: 0px 2px 0px 0px hsl(9, 75%, 61% / 0.00);
  --tracking-normal: 0em;
  --spacing: 0.25rem;

  /* Automatically computed borders - intensity can be controlled by the user by the --opaque-button-border-intensity setting */
  --sidebar-primary-border: hsl(var(--sidebar-primary));
  --sidebar-primary-border: hsl(from hsl(var(--sidebar-primary)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);
  --sidebar-accent-border: hsl(var(--sidebar-accent));
  --sidebar-accent-border: hsl(from hsl(var(--sidebar-accent)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);
  --primary-border: hsl(var(--primary));
  --primary-border: hsl(from hsl(var(--primary)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);
  --secondary-border: hsl(var(--secondary));
  --secondary-border: hsl(from hsl(var(--secondary)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);
  --muted-border: hsl(var(--muted));
  --muted-border: hsl(from hsl(var(--muted)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);
  --accent-border: hsl(var(--accent));
  --accent-border: hsl(from hsl(var(--accent)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);
  --destructive-border: hsl(var(--destructive));
  --destructive-border: hsl(from hsl(var(--destructive)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);
}

.dark {
  --button-outline: rgba(255,255,255, .10);
  --badge-outline: rgba(255,255,255, .05);

  --opaque-button-border-intensity: 9;  /* In terms of percentages */

  /* Backgrounds applied on top of other backgrounds when hovered/active */
  --elevate-1: rgba(255,255,255, .04);
  --elevate-2: rgba(255,255,255, .09);

  --background: hsl(20, 14%, 4%);
  --foreground: hsl(45, 25%, 91%);
  --border: hsl(20, 14%, 15%);
  --card: hsl(20, 14%, 8%);
  --card-foreground: hsl(45, 25%, 85%);
  --card-border: hsl(20, 14%, 15%);
  --sidebar: hsl(20, 14%, 8%);
  --sidebar-foreground: hsl(45, 25%, 85%);
  --sidebar-border: hsl(20, 14%, 15%);
  --sidebar-primary: hsl(9, 75%, 61%);
  --sidebar-primary-foreground: hsl(0, 0%, 100%);
  --sidebar-accent: hsl(25, 45%, 20%);
  --sidebar-accent-foreground: hsl(45, 25%, 85%);
  --sidebar-ring: hsl(9, 75%, 61%);
  --popover: hsl(20, 14%, 4%);
  --popover-foreground: hsl(45, 25%, 91%);
  --popover-border: hsl(20, 14%, 15%);
  --primary: hsl(9, 75%, 61%);
  --primary-foreground: hsl(0, 0%, 100%);
  --secondary: hsl(30, 15%, 52%);
  --secondary-foreground: hsl(0, 0%, 100%);
  --muted: hsl(20, 14%, 15%);
  --muted-foreground: hsl(45, 15%, 46%);
  --accent: hsl(25, 45%, 20%);
  --accent-foreground: hsl(45, 25%, 85%);
  --destructive: hsl(356.3033, 90.5579%, 54.3137%);
  --destructive-foreground: hsl(0, 0%, 100%);
  --input: hsl(20, 14%, 18%);
  --ring: hsl(9, 75%, 61%);
  --chart-1: hsl(203.8863, 88.2845%, 53.1373%);
  --chart-2: hsl(159.7826, 100%, 36.0784%);
  --chart-3: hsl(42.0290, 92.8251%, 56.2745%);
  --chart-4: hsl(147.1429, 78.5047%, 41.9608%);
  --chart-5: hsl(341.4894, 75.2000%, 50.9804%);

  --shadow-2xs: 0px 2px 0px 0px hsl(9, 75%, 61% / 0.00);
  --shadow-xs: 0px 2px 0px 0px hsl(9, 75%, 61% / 0.00);
  --shadow-sm: 0px 2px 0px 0px hsl(9, 75%, 61% / 0.00), 0px 1px 2px -1px hsl(9, 75%, 61% / 0.00);
  --shadow: 0px 2px 0px 0px hsl(9, 75%, 61% / 0.00), 0px 1px 2px -1px hsl(9, 75%, 61% / 0.00);
  --shadow-md: 0px 2px 0px 0px hsl(9, 75%, 61% / 0.00), 0px 2px 4px -1px hsl(9, 75%, 61% / 0.00);
  --shadow-lg: 0px 2px 0px 0px hsl(9, 75%, 61% / 0.00), 0px 4px 6px -1px hsl(9, 75%, 61% / 0.00);
  --shadow-xl: 0px 2px 0px 0px hsl(9, 75%, 61% / 0.00), 0px 8px 10px -1px hsl(9, 75%, 61% / 0.00);
  --shadow-2xl: 0px 2px 0px 0px hsl(9, 75%, 61% / 0.00);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

@layer utilities {
  /* Hide ugly search cancel button in Chrome until we can style it properly */
  input[type="search"]::-webkit-search-cancel-button {
    @apply hidden;
  }

  /* Placeholder styling for contentEditable div */
  [contenteditable][data-placeholder]:empty::before {
    content: attr(data-placeholder);
    color: hsl(var(--muted-foreground));
    pointer-events: none;
  }

  .no-default-hover-elevate {}
  .no-default-active-elevate {}

  .toggle-elevate::before,
  .toggle-elevate-2::before {
    content: "";
    pointer-events: none;
    position: absolute;
    inset: 0px;
    border-radius: inherit;
    z-index: -1;
  }

  .toggle-elevate.toggle-elevated::before {
    background-color: var(--elevate-2);
  }

  .border.toggle-elevate::before {
    inset: -1px;
  }

  .hover-elevate:not(.no-default-hover-elevate),
  .active-elevate:not(.no-default-active-elevate),
  .hover-elevate-2:not(.no-default-hover-elevate),
  .active-elevate-2:not(.no-default-active-elevate) {
    position: relative;
    z-index: 0;
  }

  .hover-elevate:not(.no-default-hover-elevate)::after,
  .active-elevate:not(.no-default-active-elevate)::after,
  .hover-elevate-2:not(.no-default-hover-elevate)::after,
  .active-elevate-2:not(.no-default-active-elevate)::after {
    content: "";
    pointer-events: none;
    position: absolute;
    inset: 0px;
    border-radius: inherit;
    z-index: 999;
  }

  .hover-elevate:hover:not(.no-default-hover-elevate)::after,
  .active-elevate:active:not(.no-default-active-elevate)::after {
    background-color: var(--elevate-1);
  }

  .hover-elevate-2:hover:not(.no-default-hover-elevate)::after,
  .active-elevate-2:active:not(.no-default-active-elevate)::after {
    background-color: var(--elevate-2);
  }

  .border.hover-elevate:not(.no-hover-interaction-elevate)::after,
  .border.active-elevate:not(.no-active-interaction-elevate)::after,
  .border.hover-elevate-2:not(.no-hover-interaction-elevate)::after,
  .border.active-elevate-2:not(.no-active-interaction-elevate)::after,
  .border.hover-elevate:not(.no-hover-interaction-elevate)::after {
    inset: -1px;
  }

  /* Custom spinner animation */
  .spinner {
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    width: 16px;
    height: 16px;
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  /* Error border for failed scrapers */
  .error-border {
    border-color: #dc2626 !important; /* Explicit red color */
    border-width: 2px !important;
  }
}
