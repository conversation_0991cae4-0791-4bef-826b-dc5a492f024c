import { TrendingUp } from "lucide-react";
import ScraperForm from "@/components/scraper-form";
import ScraperTable from "@/components/scraper-table";

export default function Dashboard() {
  return (
    <div className="min-h-screen bg-background text-foreground">
      <div className="container mx-auto px-4 py-8 max-w-7xl">
        <header className="mb-8">
          <h1 className="text-3xl font-bold text-foreground flex items-center gap-3">
            <TrendingUp className="text-primary" size={32} />
            PricePulse
          </h1>
          <p className="text-muted-foreground mt-2">
            Monitor product prices across multiple websites
          </p>
        </header>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
          <div className="xl:col-span-1">
            <ScraperForm />
          </div>
          
          <div className="xl:col-span-2">
            <ScraperTable />
          </div>
        </div>
      </div>
    </div>
  );
}
