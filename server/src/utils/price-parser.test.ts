import { parsePrice } from './price-parser';

describe('Price Parser', () => {
  describe('Currency symbol before number', () => {
    test('should parse $1,343.88', () => {
      expect(parsePrice('$1,343.88')).toBe('1343.88');
    });

    test('should parse $1343.88', () => {
      expect(parsePrice('$1343.88')).toBe('1343.88');
    });

    test('should parse €1.343,88 (European format)', () => {
      expect(parsePrice('€1.343,88')).toBe('1343.88');
    });

    test('should parse £1,343.88', () => {
      expect(parsePrice('£1,343.88')).toBe('1343.88');
    });

    test('should parse ₹1,34,388.00 (Indian format)', () => {
      expect(parsePrice('₹1,34,388.00')).toBe('134388');
    });
  });

  describe('Currency symbol after number', () => {
    test('should parse 1,343.88$', () => {
      expect(parsePrice('1,343.88$')).toBe('1343.88');
    });

    test('should parse 1.343,88€', () => {
      expect(parsePrice('1.343,88€')).toBe('1343.88');
    });

    test('should parse 1343.88€', () => {
      expect(parsePrice('1343.88€')).toBe('1343.88');
    });

    test('should parse 1 343,88€ (French format with space)', () => {
      expect(parsePrice('1 343,88€')).toBe('1343.88');
    });
  });

  describe('No symbol, just numbers', () => {
    test('should parse 1343.88', () => {
      expect(parsePrice('1343.88')).toBe('1343.88');
    });

    test('should parse 1,343.88', () => {
      expect(parsePrice('1,343.88')).toBe('1343.88');
    });

    test('should parse 1.343,88', () => {
      expect(parsePrice('1.343,88')).toBe('1343.88');
    });

    test('should parse 1 343.88 (space as thousands separator)', () => {
      expect(parsePrice('1 343.88')).toBe('1343.88');
    });
  });

  describe('Currency codes', () => {
    test('should parse USD 1,343.88', () => {
      expect(parsePrice('USD 1,343.88')).toBe('1343.88');
    });

    test('should parse 1,343.88 USD', () => {
      expect(parsePrice('1,343.88 USD')).toBe('1343.88');
    });

    test('should parse EUR 1.343,88', () => {
      expect(parsePrice('EUR 1.343,88')).toBe('1343.88');
    });

    test('should parse 1.343,88 EUR', () => {
      expect(parsePrice('1.343,88 EUR')).toBe('1343.88');
    });
  });

  describe('No decimal places', () => {
    test('should parse $1343', () => {
      expect(parsePrice('$1343')).toBe('1343');
    });

    test('should parse 1,343€', () => {
      expect(parsePrice('1,343€')).toBe('1343');
    });

    test('should parse 1343', () => {
      expect(parsePrice('1343')).toBe('1343');
    });

    test('should parse USD 1343', () => {
      expect(parsePrice('USD 1343')).toBe('1343');
    });
  });

  describe('Different separators', () => {
    test('should parse 1.343,88 (comma decimal, dot thousands)', () => {
      expect(parsePrice('1.343,88')).toBe('1343.88');
    });

    test('should parse 1,343.88 (dot decimal, comma thousands)', () => {
      expect(parsePrice('1,343.88')).toBe('1343.88');
    });

    test('should parse 1 343,88 (space thousands, comma decimal)', () => {
      expect(parsePrice('1 343,88')).toBe('1343.88');
    });

    test('should parse 1\'343.88 (apostrophe separator)', () => {
      expect(parsePrice('1\'343.88')).toBe('1343.88');
    });
  });

  describe('Edge cases', () => {
    test('should handle the problematic case $1,343.88 correctly', () => {
      expect(parsePrice('$1,343.88')).toBe('1343.88');
    });

    test('should return null for empty string', () => {
      expect(parsePrice('')).toBe(null);
    });

    test('should return null for null input', () => {
      expect(parsePrice(null as any)).toBe(null);
    });

    test('should return null for undefined input', () => {
      expect(parsePrice(undefined as any)).toBe(null);
    });

    test('should return null for non-numeric text', () => {
      expect(parsePrice('not a price')).toBe(null);
    });

    test('should handle multiple currency symbols', () => {
      expect(parsePrice('$$1,343.88')).toBe('1343.88');
    });

    test('should handle extra whitespace', () => {
      expect(parsePrice('  $  1,343.88  ')).toBe('1343.88');
    });

    test('should handle very large numbers', () => {
      expect(parsePrice('$1,234,567.89')).toBe('1234567.89');
    });

    test('should handle small decimal numbers', () => {
      expect(parsePrice('$0.99')).toBe('0.99');
    });

    test('should handle numbers with only cents', () => {
      expect(parsePrice('$0.50')).toBe('0.5');
    });
  });

  describe('Complex international formats', () => {
    test('should parse Swiss format with apostrophe', () => {
      expect(parsePrice('CHF 1\'343.88')).toBe('1343.88');
    });

    test('should parse German format', () => {
      expect(parsePrice('1.343,88 €')).toBe('1343.88');
    });

    test('should parse French format with spaces', () => {
      expect(parsePrice('1 343,88 €')).toBe('1343.88');
    });

    test('should parse Indian Rupee format', () => {
      expect(parsePrice('₹ 1,34,388.00')).toBe('134388');
    });
  });
});
