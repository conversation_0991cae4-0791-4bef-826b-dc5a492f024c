import { chromium } from "playwright";
import * as cheerio from "cheerio";
import type { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>ult } from "./types";
import { parsePrice } from "../utils/price-parser";

export const playwrightAdapter: ScraperAdapter = {
  name: 'playwright',
  async scrape(url: string, selector: string): Promise<ScrapeResult> {
    let browser;
    try {
      browser = await chromium.launch({
        headless: true,
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-accelerated-2d-canvas',
          '--no-first-run',
          '--no-zygote',
          '--disable-gpu'
        ]
      });

      const page = await browser.newPage({
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        viewport: { width: 1280, height: 720 }
      });

      // Try multiple wait strategies
      try {
        // First try with domcontentloaded (faster)
        console.log(`[PLAYWRIGHT] Navigating to ${url} with domcontentloaded`);
        await page.goto(url, { waitUntil: 'domcontentloaded', timeout: 20000 });

        // Wait a bit for dynamic content
        await page.waitForTimeout(2000);

        // Try to wait for the selector with a reasonable timeout
        console.log(`[PLAYWRIGHT] Waiting for selector "${selector}"`);
        await page.waitForSelector(selector, { timeout: 10000 });
        console.log(`[PLAYWRIGHT] Selector found successfully`);
      } catch (selectorError) {
        const selectorErrorMsg = selectorError instanceof Error ? selectorError.message : String(selectorError);
        console.log(`[PLAYWRIGHT] Selector wait failed (${selectorErrorMsg}), trying alternative approach`);

        // If selector wait fails, try with load event and longer wait
        try {
          console.log(`[PLAYWRIGHT] Trying with load event`);
          await page.goto(url, { waitUntil: 'load', timeout: 25000 });
          await page.waitForTimeout(3000);
        } catch (loadError) {
          const loadErrorMsg = loadError instanceof Error ? loadError.message : String(loadError);
          console.log(`[PLAYWRIGHT] Load event failed (${loadErrorMsg}), using basic navigation`);
          // Last resort: just navigate and wait
          try {
            await page.goto(url, { timeout: 30000 });
            await page.waitForTimeout(5000);
          } catch (basicError) {
            const basicErrorMsg = basicError instanceof Error ? basicError.message : String(basicError);
            console.log(`[PLAYWRIGHT] Basic navigation failed (${basicErrorMsg})`);
            throw basicError;
          }
        }
      }

      const html = await page.content();
      const $ = cheerio.load(html);

      const priceElement = $(selector);
      if (priceElement.length === 0) {
        return { price: null, error: `Selector "${selector}" not found on the page after JS execution` };
      }

      const priceText = priceElement.first().text().trim();
      const price = parsePrice(priceText);

      if (!price) {
        return { price: null, error: `Could not extract a valid price from text: "${priceText}"` };
      }

      return { price };
    } catch (error) {
      return { price: null, error: error instanceof Error ? error.message : 'Unknown error occurred' };
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }
};
