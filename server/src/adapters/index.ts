import { gotScrapingAdapter } from "./got-scraping.adapter";
import { playwrightAdapter } from "./playwright.adapter";
import type { ScraperAdapter } from "./types";

const adapters = new Map<string, ScraperAdapter>();

// Register all available adapters
adapters.set(gotScrapingAdapter.name, gotScrapingAdapter);
adapters.set(playwrightAdapter.name, playwrightAdapter);

export function getAdapter(name: string): ScraperAdapter | undefined {
  return adapters.get(name);
}

// Define which adapters to use as primary and fallback
export const PRIMARY_ADAPTER_NAME = gotScrapingAdapter.name;
export const FALLBACK_ADAPTER_NAME = playwrightAdapter.name;
