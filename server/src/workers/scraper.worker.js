// server/src/workers/scraper.worker.ts
import { parentPort, workerData } from "worker_threads";

// server/src/adapters/got-scraping.adapter.ts
import { gotScraping } from "got-scraping";
import * as cheerio from "cheerio";

// server/src/utils/price-parser.ts
var CURRENCY_SYMBOLS = ["$", "\u20AC", "\xA3", "\xA5", "\u20B9", "\u20BD", "\u20A9", "\u20AA", "\u20A6", "\u20A1", "\u20A8", "\u20B5", "\u20B4", "\u20B8", "\u20BC", "\u20BE", "\u20BF"];
var CURRENCY_CODES = ["USD", "EUR", "GBP", "JPY", "INR", "RUB", "KRW", "ILS", "NGN", "CRC", "PKR", "GHS", "UA<PERSON>", "KZ<PERSON>", "AZN", "GEL", "BTC"];
function parsePrice(priceText) {
  if (!priceText || typeof priceText !== "string") {
    return null;
  }
  let cleanText = priceText.trim().replace(/\s+/g, " ");
  if (!cleanText) {
    return null;
  }
  const numericPart = extractNumericPart(cleanText);
  if (!numericPart) {
    return null;
  }
  const parsedNumber = parseNumericPart(numericPart);
  if (parsedNumber === null || isNaN(parsedNumber) || parsedNumber < 0) {
    return null;
  }
  return parsedNumber.toString();
}
function extractNumericPart(text) {
  let cleaned = text;
  for (const code of CURRENCY_CODES) {
    const codeRegex = new RegExp(`\\b${code}\\b`, "gi");
    cleaned = cleaned.replace(codeRegex, "").trim();
  }
  for (const symbol of CURRENCY_SYMBOLS) {
    const escapedSymbol = symbol.replace(/[.*+?^${}()|[\]\\]/g, "\\$&");
    const symbolRegex = new RegExp(escapedSymbol, "g");
    cleaned = cleaned.replace(symbolRegex, "").trim();
  }
  const numericMatch = cleaned.match(/[\d\s,.']+/);
  if (!numericMatch) {
    return null;
  }
  return numericMatch[0].trim();
}
function parseNumericPart(numericText) {
  if (!numericText) {
    return null;
  }
  const patterns = [
    // Pattern 1: Comma as decimal separator (European style)
    // Examples: 1.343,88 or 1 343,88 or 1'343,88
    // But NOT 1,343 (which should be thousands separator)
    {
      regex: /^([\d\s.']+),(\d{1,2})$/,
      parse: (match) => {
        const integerPart = match[1].replace(/[\s.']/g, "");
        const decimalPart = match[2];
        return parseFloat(`${integerPart}.${decimalPart}`);
      }
    },
    // Pattern 2: Dot as decimal separator (US/UK style)
    // Examples: 1,343.88 or 1 343.88 or 1'343.88
    {
      regex: /^([\d\s,']*)\.(\d{1,3})$/,
      parse: (match) => {
        const integerPart = match[1].replace(/[\s,']/g, "");
        const decimalPart = match[2];
        return parseFloat(`${integerPart}.${decimalPart}`);
      }
    },
    // Pattern 3: No decimal places, just thousands separators
    // Examples: 1,343 or 1.343 or 1 343 or 1'343
    {
      regex: /^[\d\s,.']+$/,
      parse: (match) => {
        const fullMatch = match[0];
        if (/^\d+$/.test(fullMatch)) {
          return parseFloat(fullMatch);
        }
        const parts = fullMatch.split(/[\s,.']/);
        const lastPart = parts[parts.length - 1];
        if (parts.length === 2 && lastPart.length <= 2) {
          const integerPart = parts[0];
          const decimalPart = lastPart;
          return parseFloat(`${integerPart}.${decimalPart}`);
        } else {
          const cleaned = fullMatch.replace(/[\s,.']/g, "");
          return parseFloat(cleaned);
        }
      }
    }
  ];
  for (const pattern of patterns) {
    const match = numericText.match(pattern.regex);
    if (match) {
      try {
        const result = pattern.parse(match);
        if (!isNaN(result) && result >= 0) {
          return result;
        }
      } catch (error) {
        continue;
      }
    }
  }
  const fallbackCleaned = cleanForFallbackParsing(numericText);
  if (fallbackCleaned) {
    const fallbackResult = parseFloat(fallbackCleaned);
    if (!isNaN(fallbackResult) && fallbackResult >= 0) {
      return fallbackResult;
    }
  }
  return null;
}
function cleanForFallbackParsing(text) {
  const lastDotIndex = text.lastIndexOf(".");
  const lastCommaIndex = text.lastIndexOf(",");
  let decimalSeparatorIndex = -1;
  let decimalSeparator = "";
  if (lastDotIndex > lastCommaIndex) {
    decimalSeparatorIndex = lastDotIndex;
    decimalSeparator = ".";
  } else if (lastCommaIndex > lastDotIndex) {
    decimalSeparatorIndex = lastCommaIndex;
    decimalSeparator = ",";
  }
  if (decimalSeparatorIndex === -1) {
    return text.replace(/\D/g, "");
  }
  const afterSeparator = text.substring(decimalSeparatorIndex + 1);
  if (/^\d{1,3}$/.test(afterSeparator)) {
    const beforeSeparator = text.substring(0, decimalSeparatorIndex).replace(/\D/g, "");
    return `${beforeSeparator}.${afterSeparator}`;
  } else {
    return text.replace(/\D/g, "");
  }
}

// server/src/adapters/got-scraping.adapter.ts
var gotScrapingAdapter = {
  name: "gotScraping",
  async scrape(url, selector) {
    try {
      const response = await gotScraping.get(url, {
        timeout: { request: 15e3 }
        // Only increase timeout, let got-scraping handle the rest
      });
      const html = response.body;
      const $ = cheerio.load(html);
      const priceElement = $(selector);
      if (priceElement.length === 0) {
        return { price: null };
      }
      const priceText = priceElement.first().text().trim();
      const price = parsePrice(priceText);
      if (!price) {
        return { price: null, error: `Could not extract a valid price from text: "${priceText}"` };
      }
      return { price };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      console.log(`[GOT-SCRAPING] Failed for ${url}: ${errorMessage}`);
      return { price: null, error: errorMessage };
    }
  }
};

// server/src/adapters/playwright.adapter.ts
import { chromium } from "playwright";
import * as cheerio2 from "cheerio";
var playwrightAdapter = {
  name: "playwright",
  async scrape(url, selector) {
    let browser;
    try {
      browser = await chromium.launch({
        headless: true,
        args: [
          "--no-sandbox",
          "--disable-setuid-sandbox",
          "--disable-dev-shm-usage",
          "--disable-accelerated-2d-canvas",
          "--no-first-run",
          "--no-zygote",
          "--disable-gpu"
        ]
      });
      const page = await browser.newPage({
        userAgent: "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
        viewport: { width: 1280, height: 720 }
      });
      try {
        console.log(`[PLAYWRIGHT] Navigating to ${url} with domcontentloaded`);
        await page.goto(url, { waitUntil: "domcontentloaded", timeout: 2e4 });
        await page.waitForTimeout(2e3);
        console.log(`[PLAYWRIGHT] Waiting for selector "${selector}"`);
        await page.waitForSelector(selector, { timeout: 1e4 });
        console.log(`[PLAYWRIGHT] Selector found successfully`);
      } catch (selectorError) {
        const selectorErrorMsg = selectorError instanceof Error ? selectorError.message : String(selectorError);
        console.log(`[PLAYWRIGHT] Selector wait failed (${selectorErrorMsg}), trying alternative approach`);
        try {
          console.log(`[PLAYWRIGHT] Trying with load event`);
          await page.goto(url, { waitUntil: "load", timeout: 25e3 });
          await page.waitForTimeout(3e3);
        } catch (loadError) {
          const loadErrorMsg = loadError instanceof Error ? loadError.message : String(loadError);
          console.log(`[PLAYWRIGHT] Load event failed (${loadErrorMsg}), using basic navigation`);
          try {
            await page.goto(url, { timeout: 3e4 });
            await page.waitForTimeout(5e3);
          } catch (basicError) {
            const basicErrorMsg = basicError instanceof Error ? basicError.message : String(basicError);
            console.log(`[PLAYWRIGHT] Basic navigation failed (${basicErrorMsg})`);
            throw basicError;
          }
        }
      }
      const html = await page.content();
      const $ = cheerio2.load(html);
      const priceElement = $(selector);
      if (priceElement.length === 0) {
        return { price: null, error: `Selector "${selector}" not found on the page after JS execution` };
      }
      const priceText = priceElement.first().text().trim();
      const price = parsePrice(priceText);
      if (!price) {
        return { price: null, error: `Could not extract a valid price from text: "${priceText}"` };
      }
      return { price };
    } catch (error) {
      return { price: null, error: error instanceof Error ? error.message : "Unknown error occurred" };
    } finally {
      if (browser) {
        await browser.close();
      }
    }
  }
};

// server/src/adapters/index.ts
var adapters = /* @__PURE__ */ new Map();
adapters.set(gotScrapingAdapter.name, gotScrapingAdapter);
adapters.set(playwrightAdapter.name, playwrightAdapter);
function getAdapter(name) {
  return adapters.get(name);
}
var PRIMARY_ADAPTER_NAME = gotScrapingAdapter.name;
var FALLBACK_ADAPTER_NAME = playwrightAdapter.name;

// server/src/workers/scraper.worker.ts
async function run() {
  if (!parentPort) throw new Error("This script must be run as a worker thread.");
  const { id, url, selector, primaryAdapter, fallbackAdapter } = workerData;
  console.log(`[WORKER] Job ${id}: Starting scrape for ${url}`);
  try {
    console.log(`[WORKER] Job ${id}: Step 1 - Trying primary adapter (${primaryAdapter})`);
    const primaryAdapterInstance = getAdapter(primaryAdapter);
    if (!primaryAdapterInstance) {
      throw new Error(`Primary adapter "${primaryAdapter}" not found.`);
    }
    let result = await primaryAdapterInstance.scrape(url, selector);
    if (!result.price) {
      console.log(`[WORKER] Job ${id}: Step 2 - Primary adapter failed (${result.error || "no price found"}). Trying fallback adapter (${fallbackAdapter})`);
      const fallbackAdapterInstance = getAdapter(fallbackAdapter);
      if (!fallbackAdapterInstance) {
        throw new Error(`Fallback adapter "${fallbackAdapter}" not found.`);
      }
      result = await fallbackAdapterInstance.scrape(url, selector);
      if (!result.price) {
        console.log(`[WORKER] Job ${id}: Step 3 - Both adapters failed. Final error: ${result.error || "no price found"}`);
        result = {
          price: null,
          error: `Both ${primaryAdapter} and ${fallbackAdapter} failed to extract price. Last error: ${result.error || "no price found"}`
        };
      } else {
        console.log(`[WORKER] Job ${id}: Step 3 - Fallback adapter succeeded! Price extracted: ${result.price}`);
      }
    } else {
      console.log(`[WORKER] Job ${id}: Step 2 - Primary adapter succeeded! Price extracted: ${result.price}`);
    }
    console.log(`[WORKER] Job ${id}: Completed successfully, sending result to main thread`);
    parentPort.postMessage(result);
  } catch (error) {
    console.error(`Job ${id}: Worker execution failed:`, error);
    const result = {
      price: null,
      error: error instanceof Error ? error.message : "Worker execution failed"
    };
    parentPort.postMessage(result);
  }
}
run();
