// Simple test for average price calculation without imports
// Copy the functions directly for testing

function calculate90DayAverage(priceHistory) {
  if (!priceHistory || priceHistory.length === 0) {
    return null;
  }

  // Calculate the date 90 days ago
  const ninetyDaysAgo = new Date();
  ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

  // Filter price history to only include entries from the last 90 days
  const recentHistory = priceHistory.filter(entry => {
    const entryDate = new Date(entry.timestamp);
    return entryDate >= ninetyDaysAgo;
  });

  if (recentHistory.length === 0) {
    return null;
  }

  // Calculate the average price
  let totalPrice = 0;
  let validPriceCount = 0;

  for (const entry of recentHistory) {
    const price = parseFloat(entry.price);
    if (!isNaN(price) && price > 0) {
      totalPrice += price;
      validPriceCount++;
    }
  }

  if (validPriceCount === 0) {
    return null;
  }

  const averagePrice = totalPrice / validPriceCount;

  // Return the average price rounded to 2 decimal places
  return averagePrice.toFixed(2);
}

function calculatePriceStatistics(priceHistory) {
  if (!priceHistory || priceHistory.length === 0) {
    return {
      currentPrice: null,
      lowestPrice: null,
      previousPrice: null,
      averagePrice: null,
    };
  }

  // Sort by timestamp descending (newest first)
  const sortedHistory = [...priceHistory].sort((a, b) =>
    new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );

  // Get current price (most recent entry)
  const currentPrice = sortedHistory[0]?.price || null;

  // Calculate lowest price from all history
  let lowestPrice = null;
  for (const entry of priceHistory) {
    const price = parseFloat(entry.price);
    if (!isNaN(price) && price > 0) {
      if (lowestPrice === null || price < parseFloat(lowestPrice)) {
        lowestPrice = entry.price;
      }
    }
  }

  // Get previous price (most recent entry from a different day than today)
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const previousHistory = sortedHistory.find(entry => {
    const entryDate = new Date(entry.timestamp);
    entryDate.setHours(0, 0, 0, 0);
    return entryDate.getTime() < today.getTime();
  });
  const previousPrice = previousHistory?.price || null;

  // Calculate 90-day average
  const averagePrice = calculate90DayAverage(priceHistory);

  return {
    currentPrice,
    lowestPrice,
    previousPrice,
    averagePrice,
  };
}

// Test data - simulate price history for the last 90 days
const testPriceHistory = [
  {
    id: '1',
    scraperId: 'test-scraper',
    price: '100.00',
    timestamp: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000) // 1 day ago
  },
  {
    id: '2',
    scraperId: 'test-scraper',
    price: '95.00',
    timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000) // 5 days ago
  },
  {
    id: '3',
    scraperId: 'test-scraper',
    price: '110.00',
    timestamp: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000) // 10 days ago
  },
  {
    id: '4',
    scraperId: 'test-scraper',
    price: '105.00',
    timestamp: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) // 30 days ago
  },
  {
    id: '5',
    scraperId: 'test-scraper',
    price: '90.00',
    timestamp: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000) // 60 days ago
  },
  {
    id: '6',
    scraperId: 'test-scraper',
    price: '120.00',
    timestamp: new Date(Date.now() - 100 * 24 * 60 * 60 * 1000) // 100 days ago (should be excluded)
  }
];

console.log('Testing 90-day average price calculation...\n');

// Test 1: Calculate 90-day average
console.log('Test 1: 90-day average calculation');
const average = calculate90DayAverage(testPriceHistory);
console.log('Price history (last 90 days):');
testPriceHistory.forEach(entry => {
  const daysAgo = Math.floor((Date.now() - new Date(entry.timestamp).getTime()) / (24 * 60 * 60 * 1000));
  const included = daysAgo <= 90;
  console.log(`  ${entry.price} (${daysAgo} days ago) ${included ? '✓' : '✗ excluded'}`);
});

console.log(`\nCalculated 90-day average: ${average}`);

// Expected: (100 + 95 + 110 + 105 + 90) / 5 = 500 / 5 = 100.00
const expectedAverage = ((100 + 95 + 110 + 105 + 90) / 5).toFixed(2);
console.log(`Expected average: ${expectedAverage}`);
console.log(`Test 1 ${average === expectedAverage ? 'PASSED' : 'FAILED'}\n`);

// Test 2: Calculate all price statistics
console.log('Test 2: Complete price statistics');
const stats = calculatePriceStatistics(testPriceHistory);
console.log('Price statistics:', stats);

// Verify current price (most recent)
const expectedCurrentPrice = '100.00';
console.log(`Current price: ${stats.currentPrice} (expected: ${expectedCurrentPrice}) ${stats.currentPrice === expectedCurrentPrice ? '✓' : '✗'}`);

// Verify lowest price (from all history)
const expectedLowestPrice = '90.00';
console.log(`Lowest price: ${stats.lowestPrice} (expected: ${expectedLowestPrice}) ${stats.lowestPrice === expectedLowestPrice ? '✓' : '✗'}`);

// Verify average price
console.log(`Average price: ${stats.averagePrice} (expected: ${expectedAverage}) ${stats.averagePrice === expectedAverage ? '✓' : '✗'}`);

console.log(`\nTest 2 ${
  stats.currentPrice === expectedCurrentPrice && 
  stats.lowestPrice === expectedLowestPrice && 
  stats.averagePrice === expectedAverage ? 'PASSED' : 'FAILED'
}`);

// Test 3: Empty price history
console.log('\nTest 3: Empty price history');
const emptyStats = calculatePriceStatistics([]);
console.log('Empty stats:', emptyStats);
const allNull = Object.values(emptyStats).every(value => value === null);
console.log(`Test 3 ${allNull ? 'PASSED' : 'FAILED'}`);

// Test 4: Price history older than 90 days
console.log('\nTest 4: Price history older than 90 days');
const oldPriceHistory = [
  {
    id: '1',
    scraperId: 'test-scraper',
    price: '200.00',
    timestamp: new Date(Date.now() - 100 * 24 * 60 * 60 * 1000) // 100 days ago
  }
];
const oldAverage = calculate90DayAverage(oldPriceHistory);
console.log(`Old price history average: ${oldAverage} (expected: null)`);
console.log(`Test 4 ${oldAverage === null ? 'PASSED' : 'FAILED'}`);

console.log('\n=== All Tests Complete ===');
