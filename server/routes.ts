import type { Express } from "express";
import { storage } from "./storage";
import { insertScraperSchema, updateScraperSchema } from "@shared/schema";
import { z } from "zod";

export function registerRestRoutes(app: Express) {

  // Get all scrapers
  app.get("/api/scrapers", async (req, res) => {
    try {
      const scrapers = await storage.getAllScrapers();
      res.json(scrapers);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch scrapers" });
    }
  });

  // Get single scraper
  app.get("/api/scrapers/:id", async (req, res) => {
    try {
      const scraper = await storage.getScraper(req.params.id);
      if (!scraper) {
        return res.status(404).json({ message: "Scraper not found" });
      }
      res.json(scraper);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch scraper" });
    }
  });

  // Create new scraper
  app.post("/api/scrapers", async (req, res) => {
    try {
      const scraperData = insertScraperSchema.parse(req.body);
      const scraper = await storage.createScraper(scraperData);
      res.status(201).json(scraper);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid scraper data", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to create scraper" });
    }
  });

  // Update scraper
  app.put("/api/scrapers/:id", async (req, res) => {
    try {
      const updates = updateScraperSchema.parse(req.body);
      const scraper = await storage.updateScraper(req.params.id, updates);
      if (!scraper) {
        return res.status(404).json({ message: "Scraper not found" });
      }
      res.json(scraper);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: "Invalid update data", errors: error.errors });
      }
      res.status(500).json({ message: "Failed to update scraper" });
    }
  });

  // Delete scraper
  app.delete("/api/scrapers/:id", async (req, res) => {
    try {
      const deleted = await storage.deleteScraper(req.params.id);
      if (!deleted) {
        return res.status(404).json({ message: "Scraper not found" });
      }
      res.json({ message: "Scraper deleted successfully" });
    } catch (error) {
      res.status(500).json({ message: "Failed to delete scraper" });
    }
  });


  // Get price history for a scraper
  app.get("/api/scrapers/:id/history", async (req, res) => {
    try {
      const history = await storage.getPriceHistory(req.params.id);
      res.json(history);
    } catch (error) {
      res.status(500).json({ message: "Failed to fetch price history" });
    }
  });

}
