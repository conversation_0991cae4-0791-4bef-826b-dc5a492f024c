// <PERSON>ript to trigger a price update and see the average calculation logs
import fs from 'fs';

// Read current database
const dbData = JSON.parse(fs.readFileSync('db.json', 'utf8'));

// Add a new price entry for the ThinkPad to trigger average calculation
const thinkpadId = '1f417294-782a-47d1-a7ea-e63cf7fbd300';
const newPrice = '3199.99'; // A different price to trigger calculation

console.log('=== SIMULATING PRICE UPDATE ===');
console.log(`Adding new price ${newPrice} for ThinkPad scraper ${thinkpadId}`);

// Add new price history entry
const newEntry = {
  id: `test-${Date.now()}`,
  scraperId: thinkpadId,
  price: newPrice,
  timestamp: new Date().toISOString()
};

dbData.priceHistory.push(newEntry);

// Write back to database
fs.writeFileSync('db.json', JSON.stringify(dbData, null, 2));

console.log('✅ New price entry added to database');
console.log('Now the server should detect this change and recalculate the average price');

// Show current price history for ThinkPad
const thinkpadHistory = dbData.priceHistory.filter(h => h.scraperId === thinkpadId);
console.log('\nCurrent ThinkPad price history:');
thinkpadHistory.forEach((entry, index) => {
  const daysAgo = Math.floor((Date.now() - new Date(entry.timestamp).getTime()) / (24 * 60 * 60 * 1000));
  console.log(`  ${index + 1}. ${entry.price} (${daysAgo} days ago)`);
});

// Calculate expected new average
const prices = thinkpadHistory.map(h => parseFloat(h.price));
const expectedAverage = (prices.reduce((sum, price) => sum + price, 0) / prices.length).toFixed(2);
console.log(`\nExpected new average: ${expectedAverage}`);

console.log('\n🔄 Now trigger an update in the browser to see the detailed logs!');
