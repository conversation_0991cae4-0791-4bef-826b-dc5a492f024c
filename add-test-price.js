// Add a test price to trigger average calculation
import fs from 'fs';

// Read current database
const dbData = JSON.parse(fs.readFileSync('db.json', 'utf8'));

// Add a new price entry for the ThinkPad with a different price
const thinkpadId = '1f417294-782a-47d1-a7ea-e63cf7fbd300';
const newPrice = '3299.99'; // Different price to trigger calculation

console.log('Adding test price entry...');

// Add new price history entry
const newEntry = {
  id: `test-${Date.now()}`,
  scraperId: thinkpadId,
  price: newPrice,
  timestamp: new Date().toISOString()
};

dbData.priceHistory.push(newEntry);

// Write back to database
fs.writeFileSync('db.json', JSON.stringify(dbData, null, 2));

console.log(`✅ Added price ${newPrice} for ThinkPad`);
console.log('Now trigger an update in the browser to see the average calculation log!');
