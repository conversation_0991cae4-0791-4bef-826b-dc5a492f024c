# System Patterns

## Architecture Overview

PricePulse follows a client-server architecture.

- **Client:** A web-based frontend built with React (likely using Vite, based on the project structure). This is the user-facing part of the application.
- **Server:** A backend service (likely Node.js with Express or a similar framework) that handles the core logic, including web scraping, data storage, and API endpoints.

## Data Flow

1. **User Interaction:** The user interacts with the React client to add a product URL.
2. **API Request:** The client sends an API request to the server with the URL.
3. **Scraping:** The server receives the request and initiates a web scraping process to fetch the product data from the provided URL.
4. **Data Storage:** The scraped data (product name, price, etc.) is stored in a database (likely a simple JSON file or a more robust database).
5. **Data Retrieval:** The client fetches the tracked product data from the server to display to the user.
6. **Scheduled Updates:** The server periodically re-scrapes the product URLs to check for price changes.

## Key Components

- **`client/`:** Contains the React frontend code.
  - **`src/components/`:** Reusable UI components.
  - **`src/pages/`:** Top-level page components.
- **`server/`:** Contains the backend server code.
  - **`routes.ts`:** Defines the API endpoints.
  - **`storage.ts`:** Manages data persistence.
- **`shared/`:** Contains code shared between the client and server, such as data schemas.
