# Tech Context

## Frontend

- **Framework:** React
- **Language:** TypeScript (`.tsx` files)
- **Build Tool:** Vite (`vite.config.ts`)
- **Styling:** Tailwind CSS (`tailwind.config.ts`) and CSS (`index.css`)
- **UI Components:** A mix of custom components and potentially a library like ShadCN/UI, given the `components/ui` directory structure.

## Backend

- **Runtime:** Node.js
- **Language:** TypeScript (`.ts` files)
- **Framework:** Likely Express or a similar lightweight framework.
- **Database:** A simple JSON file (`db.json`) is used for data storage.

## Shared

- **`shared/schema.ts`:** A TypeScript file is used to define a shared data schema between the client and server, ensuring type safety across the application.

## Development Environment

- **Package Manager:** npm (inferred from `package-lock.json`)
- **Configuration:** The project is configured with `tsconfig.json` for TypeScript and `drizzle.config.ts` which suggests the use of Drizzle ORM.
