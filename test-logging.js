// Test script to trigger average price calculation and see the logs
import fs from 'fs';

// Copy the updated calculation function with logging
function calculate90DayAverage(priceHistory, scraperId) {
  const logPrefix = scraperId ? `[AVERAGE-CALC] Scraper ${scraperId}:` : '[AVERAGE-CALC]';
  
  if (!priceHistory || priceHistory.length === 0) {
    console.log(`${logPrefix} No price history available`);
    return null;
  }

  console.log(`${logPrefix} Calculating average from ${priceHistory.length} price history entries`);

  // Calculate the date 90 days ago
  const ninetyDaysAgo = new Date();
  ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

  // Filter price history to only include entries from the last 90 days
  // and only include entries with valid prices
  const validRecentHistory = priceHistory.filter(entry => {
    if (!entry || !entry.timestamp || !entry.price) {
      console.log(`${logPrefix} Skipping invalid entry:`, entry);
      return false;
    }
    
    const entryDate = new Date(entry.timestamp);
    if (isNaN(entryDate.getTime())) {
      console.log(`${logPrefix} Skipping entry with invalid timestamp:`, entry.timestamp);
      return false;
    }
    
    const price = parseFloat(entry.price);
    if (isNaN(price) || price <= 0) {
      console.log(`${logPrefix} Skipping entry with invalid price:`, entry.price);
      return false;
    }
    
    const isRecent = entryDate >= ninetyDaysAgo;
    const daysAgo = Math.floor((Date.now() - entryDate.getTime()) / (24 * 60 * 60 * 1000));
    console.log(`${logPrefix} Price ${entry.price} from ${daysAgo} days ago - ${isRecent ? 'included' : 'excluded (>90 days)'}`);
    
    return isRecent;
  });

  if (validRecentHistory.length === 0) {
    console.log(`${logPrefix} No valid recent price history found (within 90 days)`);
    return null;
  }

  console.log(`${logPrefix} Using ${validRecentHistory.length} valid entries for calculation`);

  // Calculate the average price
  let totalPrice = 0;
  
  for (const entry of validRecentHistory) {
    const price = parseFloat(entry.price);
    totalPrice += price;
    console.log(`${logPrefix} Adding price ${price} to total (running total: ${totalPrice.toFixed(2)})`);
  }

  const averagePrice = totalPrice / validRecentHistory.length;
  const result = averagePrice.toFixed(2);
  
  console.log(`${logPrefix} Final calculation: ${totalPrice.toFixed(2)} / ${validRecentHistory.length} = ${result}`);
  
  // Return the average price rounded to 2 decimal places
  return result;
}

// Read the current database
const dbData = JSON.parse(fs.readFileSync('db.json', 'utf8'));

console.log('=== TESTING AVERAGE PRICE CALCULATION WITH DETAILED LOGGING ===\n');

// Test the ThinkPad scraper specifically
const thinkpadId = '1f417294-782a-47d1-a7ea-e63cf7fbd300';
const thinkpadScraper = dbData.scrapers.find(s => s.id === thinkpadId);
const thinkpadHistory = dbData.priceHistory.filter(h => h.scraperId === thinkpadId);

console.log(`Testing scraper: ${thinkpadScraper.itemName.substring(0, 50)}...`);
console.log(`Scraper ID: ${thinkpadId}`);
console.log(`Current average in DB: ${thinkpadScraper.averagePrice}`);
console.log('');

// Trigger the calculation with logging
const calculatedAverage = calculate90DayAverage(thinkpadHistory, thinkpadId);

console.log('\n=== SUMMARY ===');
console.log(`Database average: ${thinkpadScraper.averagePrice}`);
console.log(`Calculated average: ${calculatedAverage}`);
console.log(`Match: ${thinkpadScraper.averagePrice === calculatedAverage ? 'YES' : 'NO'}`);

// Test with a different scraper that has only one price
console.log('\n=== TESTING SINGLE PRICE SCRAPER ===');
const asusId = 'ebd56f46-b0ea-40c6-ba79-57f940eb24d5';
const asusScraper = dbData.scrapers.find(s => s.id === asusId);
const asusHistory = dbData.priceHistory.filter(h => h.scraperId === asusId);

console.log(`Testing scraper: ${asusScraper.itemName.substring(0, 50)}...`);
console.log(`Scraper ID: ${asusId}`);
console.log('');

const asusCalculatedAverage = calculate90DayAverage(asusHistory, asusId);

console.log('\n=== SINGLE PRICE SUMMARY ===');
console.log(`Database average: ${asusScraper.averagePrice}`);
console.log(`Calculated average: ${asusCalculatedAverage}`);
console.log(`Match: ${asusScraper.averagePrice === asusCalculatedAverage ? 'YES' : 'NO'}`);
