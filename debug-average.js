// Debug script to test average price calculation with real data
import fs from 'fs';

// Copy the functions directly for testing
function calculate90DayAverage(priceHistory) {
  console.log('calculate90DayAverage called with:', priceHistory.length, 'entries');
  
  if (!priceHistory || priceHistory.length === 0) {
    console.log('No price history provided');
    return null;
  }

  // Calculate the date 90 days ago
  const ninetyDaysAgo = new Date();
  ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);
  console.log('90 days ago date:', ninetyDaysAgo.toISOString());

  // Filter price history to only include entries from the last 90 days
  const recentHistory = priceHistory.filter(entry => {
    const entryDate = new Date(entry.timestamp);
    const isRecent = entryDate >= ninetyDaysAgo;
    console.log(`Entry ${entry.price} from ${entryDate.toISOString()} - ${isRecent ? 'included' : 'excluded'}`);
    return isRecent;
  });

  console.log('Recent history entries:', recentHistory.length);

  if (recentHistory.length === 0) {
    console.log('No recent history found');
    return null;
  }

  // Calculate the average price
  let totalPrice = 0;
  let validPriceCount = 0;

  for (const entry of recentHistory) {
    const price = parseFloat(entry.price);
    console.log(`Processing price: ${entry.price} -> ${price}`);
    if (!isNaN(price) && price > 0) {
      totalPrice += price;
      validPriceCount++;
      console.log(`Added to total. Running total: ${totalPrice}, count: ${validPriceCount}`);
    } else {
      console.log(`Invalid price: ${entry.price}`);
    }
  }

  if (validPriceCount === 0) {
    console.log('No valid prices found');
    return null;
  }

  const averagePrice = totalPrice / validPriceCount;
  console.log(`Final calculation: ${totalPrice} / ${validPriceCount} = ${averagePrice}`);
  
  // Return the average price rounded to 2 decimal places
  const result = averagePrice.toFixed(2);
  console.log('Final result:', result);
  return result;
}

function calculatePriceStatistics(priceHistory) {
  console.log('\n=== calculatePriceStatistics called ===');
  
  if (!priceHistory || priceHistory.length === 0) {
    console.log('No price history provided to calculatePriceStatistics');
    return {
      currentPrice: null,
      lowestPrice: null,
      previousPrice: null,
      averagePrice: null,
    };
  }

  console.log('Price history entries:', priceHistory.length);

  // Sort by timestamp descending (newest first)
  const sortedHistory = [...priceHistory].sort((a, b) => 
    new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
  );

  console.log('Sorted history (newest first):');
  sortedHistory.forEach((entry, index) => {
    console.log(`  ${index}: ${entry.price} at ${entry.timestamp}`);
  });

  // Get current price (most recent entry)
  const currentPrice = sortedHistory[0]?.price || null;
  console.log('Current price:', currentPrice);

  // Calculate lowest price from all history
  let lowestPrice = null;
  for (const entry of priceHistory) {
    const price = parseFloat(entry.price);
    if (!isNaN(price) && price > 0) {
      if (lowestPrice === null || price < parseFloat(lowestPrice)) {
        lowestPrice = entry.price;
      }
    }
  }
  console.log('Lowest price:', lowestPrice);

  // Get previous price (most recent entry from a different day than today)
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  console.log('Today (start of day):', today.toISOString());

  const previousHistory = sortedHistory.find(entry => {
    const entryDate = new Date(entry.timestamp);
    entryDate.setHours(0, 0, 0, 0);
    const isDifferentDay = entryDate.getTime() < today.getTime();
    console.log(`Checking ${entry.price} from ${entryDate.toISOString()} - different day: ${isDifferentDay}`);
    return isDifferentDay;
  });
  const previousPrice = previousHistory?.price || null;
  console.log('Previous price:', previousPrice);

  // Calculate 90-day average
  console.log('\n--- Calculating 90-day average ---');
  const averagePrice = calculate90DayAverage(priceHistory);

  const result = {
    currentPrice,
    lowestPrice,
    previousPrice,
    averagePrice,
  };
  
  console.log('Final statistics:', result);
  return result;
}

// Read the actual database
const dbData = JSON.parse(fs.readFileSync('db.json', 'utf8'));

// Find the ThinkPad scraper
const thinkpadId = '1f417294-782a-47d1-a7ea-e63cf7fbd300';
const thinkpadScraper = dbData.scrapers.find(s => s.id === thinkpadId);
const thinkpadHistory = dbData.priceHistory.filter(h => h.scraperId === thinkpadId);

console.log('=== DEBUGGING THINKPAD AVERAGE PRICE ===');
console.log('Scraper:', thinkpadScraper?.itemName);
console.log('Current averagePrice in DB:', thinkpadScraper?.averagePrice);
console.log('Price history entries found:', thinkpadHistory.length);

console.log('\nPrice history:');
thinkpadHistory.forEach((entry, index) => {
  console.log(`  ${index + 1}: ${entry.price} at ${entry.timestamp}`);
});

console.log('\n=== TESTING CALCULATION ===');
const stats = calculatePriceStatistics(thinkpadHistory);

console.log('\n=== SUMMARY ===');
console.log('Expected average price:', stats.averagePrice);
console.log('Current DB average price:', thinkpadScraper?.averagePrice);
console.log('Match:', stats.averagePrice === thinkpadScraper?.averagePrice ? 'YES' : 'NO');
