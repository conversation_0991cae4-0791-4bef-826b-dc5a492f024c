// Test script to verify average price calculation and database updates
import fs from 'fs';

// Copy the calculation function for testing
function calculate90DayAverage(priceHistory) {
  if (!priceHistory || priceHistory.length === 0) {
    return null;
  }

  // Calculate the date 90 days ago
  const ninetyDaysAgo = new Date();
  ninetyDaysAgo.setDate(ninetyDaysAgo.getDate() - 90);

  // Filter price history to only include entries from the last 90 days
  // and only include entries with valid prices
  const validRecentHistory = priceHistory.filter(entry => {
    if (!entry || !entry.timestamp || !entry.price) {
      return false;
    }
    
    const entryDate = new Date(entry.timestamp);
    if (isNaN(entryDate.getTime())) {
      return false;
    }
    
    const price = parseFloat(entry.price);
    if (isNaN(price) || price <= 0) {
      return false;
    }
    
    return entryDate >= ninetyDaysAgo;
  });

  if (validRecentHistory.length === 0) {
    return null;
  }

  // Calculate the average price
  let totalPrice = 0;
  
  for (const entry of validRecentHistory) {
    totalPrice += parseFloat(entry.price);
  }

  const averagePrice = totalPrice / validRecentHistory.length;
  
  // Return the average price rounded to 2 decimal places
  return averagePrice.toFixed(2);
}

// Read the current database
const dbData = JSON.parse(fs.readFileSync('db.json', 'utf8'));

console.log('=== TESTING AVERAGE PRICE CALCULATION ===\n');

// Test each scraper
dbData.scrapers.forEach((scraper, index) => {
  console.log(`${index + 1}. Testing scraper: ${scraper.itemName.substring(0, 50)}...`);
  console.log(`   ID: ${scraper.id}`);
  console.log(`   Current average in DB: ${scraper.averagePrice}`);
  
  // Get price history for this scraper
  const history = dbData.priceHistory.filter(h => h.scraperId === scraper.id);
  console.log(`   Price history entries: ${history.length}`);
  
  if (history.length > 0) {
    console.log('   Price history:');
    history.forEach((entry, i) => {
      const daysAgo = Math.floor((Date.now() - new Date(entry.timestamp).getTime()) / (24 * 60 * 60 * 1000));
      console.log(`     ${i + 1}. ${entry.price} (${daysAgo} days ago)`);
    });
    
    // Calculate what the average should be
    const calculatedAverage = calculate90DayAverage(history);
    console.log(`   Calculated average: ${calculatedAverage}`);
    
    if (calculatedAverage !== scraper.averagePrice) {
      console.log(`   ⚠️  MISMATCH! DB has ${scraper.averagePrice}, should be ${calculatedAverage}`);
      
      // Update the database
      scraper.averagePrice = calculatedAverage;
      console.log(`   ✅ Updated DB average to: ${calculatedAverage}`);
    } else {
      console.log(`   ✅ Average price is correct`);
    }
  } else {
    console.log(`   ℹ️  No price history found`);
    if (scraper.averagePrice !== null) {
      console.log(`   ⚠️  DB has average ${scraper.averagePrice} but no history, setting to null`);
      scraper.averagePrice = null;
    }
  }
  
  console.log('');
});

// Write the updated database back
fs.writeFileSync('db.json', JSON.stringify(dbData, null, 2));
console.log('✅ Database updated with correct average prices');

console.log('\n=== SUMMARY ===');
dbData.scrapers.forEach((scraper, index) => {
  const history = dbData.priceHistory.filter(h => h.scraperId === scraper.id);
  console.log(`${index + 1}. ${scraper.itemName.substring(0, 40)}...`);
  console.log(`   History entries: ${history.length}, Average: ${scraper.averagePrice}`);
});
