<html lang="en" class="dark">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Price Scraper Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    borderRadius: {
                        lg: "var(--radius)",
                        md: "calc(var(--radius) - 2px)",
                        sm: "calc(var(--radius) - 4px)",
                    },
                    colors: {
                        background: "var(--background)",
                        foreground: "var(--foreground)",
                        card: {
                            DEFAULT: "var(--card)",
                            foreground: "var(--card-foreground)",
                        },
                        popover: {
                            DEFAULT: "var(--popover)",
                            foreground: "var(--popover-foreground)",
                        },
                        primary: {
                            DEFAULT: "var(--primary)",
                            foreground: "var(--primary-foreground)",
                        },
                        secondary: {
                            DEFAULT: "var(--secondary)",
                            foreground: "var(--secondary-foreground)",
                        },
                        muted: {
                            DEFAULT: "var(--muted)",
                            foreground: "var(--muted-foreground)",
                        },
                        accent: {
                            DEFAULT: "var(--accent)",
                            foreground: "var(--accent-foreground)",
                        },
                        destructive: {
                            DEFAULT: "var(--destructive)",
                            foreground: "var(--destructive-foreground)",
                        },
                        border: "var(--border)",
                        input: "var(--input)",
                        ring: "var(--ring)",
                        chart: {
                            "1": "var(--chart-1)",
                            "2": "var(--chart-2)",
                            "3": "var(--chart-3)",
                            "4": "var(--chart-4)",
                            "5": "var(--chart-5)",
                        }
                    },
                    fontFamily: {
                        sans: ["var(--font-sans)"],
                        serif: ["var(--font-serif)"],
                        mono: ["var(--font-mono)"],
                    }
                },
            }
        };
    </script>
    <style>
        :root {
            --card: #101011;
            --ring: hsl(221.74, 13.14%, 34.31%);
            --input: #2b2b2b;
            --muted: #404040;
            --accent: hsl(0, 0%, 92.16%);
            --border: #2b2b2b;
            --radius: 0.4rem;
            --shadow: 0px 2px 0px 0px hsl(9, 75%, 61% / 0.00), 0px 1px 2px -1px hsl(9, 75%, 61% / 0.00);
            --chart-1: hsl(203.8863, 88.2845%, 53.1373%);
            --chart-2: hsl(159.7826, 100%, 36.0784%);
            --chart-3: hsl(42.0290, 92.8251%, 56.2745%);
            --chart-4: hsl(147.1429, 78.5047%, 41.9608%);
            --chart-5: hsl(341.4894, 75.2000%, 50.9804%);
            --popover: hsl(45, 25%, 97%);
            --primary: #f5f5f5;
            --sidebar: hsl(45, 25%, 97%);
            --spacing: 0.25rem;
            --font-mono: Menlo, monospace;
            --font-sans: 'Inter', sans-serif;
            --secondary: #ebebeb;
            --shadow-lg: 0px 2px 0px 0px hsl(9, 75%, 61% / 0.00), 0px 4px 6px -1px hsl(9, 75%, 61% / 0.00);
            --shadow-md: 0px 2px 0px 0px hsl(9, 75%, 61% / 0.00), 0px 2px 4px -1px hsl(9, 75%, 61% / 0.00);
            --shadow-sm: 0px 2px 0px 0px hsl(9, 75%, 61% / 0.00), 0px 1px 2px -1px hsl(9, 75%, 61% / 0.00);
            --shadow-xl: 0px 2px 0px 0px hsl(9, 75%, 61% / 0.00), 0px 8px 10px -1px hsl(9, 75%, 61% / 0.00);
            --shadow-xs: 0px 2px 0px 0px hsl(9, 75%, 61% / 0.00);
            --background: #090909;
            --font-serif: 'Inter', sans-serif;
            --foreground: #ebebeb;
            --shadow-2xl: 0px 2px 0px 0px hsl(9, 75%, 61% / 0.00);
            --shadow-2xs: 0px 2px 0px 0px hsl(9, 75%, 61% / 0.00);
            --destructive: hsl(356.3033, 90.5579%, 54.3137%);
            --sidebar-ring: hsl(9, 75%, 61%);
            --sidebar-accent: hsl(25, 45%, 80%);
            --sidebar-border: hsl(45, 15%, 80%);
            --card-foreground: hsl(0, 0%, 100%);
            --sidebar-primary: hsl(9, 75%, 61%);
            --tracking-normal: 0em;
            --muted-foreground: #ababab;
            --accent-foreground: hsl(0, 0%, 0%);
            --popover-foreground: hsl(0, 0%, 100%);
            --primary-foreground: #0d0d0d;
            --sidebar-foreground: hsl(20, 14%, 17%);
            --secondary-foreground: #0d0d0d;
            --destructive-foreground: #ffffff;
            --sidebar-accent-foreground: hsl(20, 14%, 17%);
            --sidebar-primary-foreground: hsl(0, 0%, 100%);
        }

        .dark {
            --card: hsl(20, 14%, 8%);
            --ring: hsl(9, 75%, 61%);
            --input: hsl(20, 14%, 18%);
            --muted: hsl(20, 14%, 15%);
            --accent: hsl(25, 45%, 20%);
            --border: hsl(20, 14%, 15%);
            --chart-1: hsl(203.8863, 88.2845%, 53.1373%);
            --chart-2: hsl(159.7826, 100%, 36.0784%);
            --chart-3: hsl(42.0290, 92.8251%, 56.2745%);
            --chart-4: hsl(147.1429, 78.5047%, 41.9608%);
            --chart-5: hsl(341.4894, 75.2000%, 50.9804%);
            --popover: hsl(20, 14%, 4%);
            --primary: hsl(9, 75%, 61%);
            --sidebar: hsl(20, 14%, 8%);
            --secondary: hsl(30, 15%, 52%);
            --background: hsl(20, 14%, 4%);
            --foreground: hsl(45, 25%, 91%);
            --destructive: hsl(356.3033, 90.5579%, 54.3137%);
            --sidebar-ring: hsl(9, 75%, 61%);
            --sidebar-accent: hsl(25, 45%, 20%);
            --sidebar-border: hsl(20, 14%, 15%);
            --card-foreground: hsl(45, 25%, 85%);
            --sidebar-primary: hsl(9, 75%, 61%);
            --muted-foreground: hsl(45, 15%, 46%);
            --accent-foreground: hsl(45, 25%, 85%);
            --popover-foreground: hsl(45, 25%, 91%);
            --primary-foreground: hsl(0, 0%, 100%);
            --sidebar-foreground: hsl(45, 25%, 85%);
            --secondary-foreground: hsl(0, 0%, 100%);
            --destructive-foreground: hsl(0, 0%, 100%);
            --sidebar-accent-foreground: hsl(45, 25%, 85%);
            --sidebar-primary-foreground: hsl(0, 0%, 100%);
        }

        body {
            font-family: var(--font-sans);
        }

        .spinner {
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            width: 16px;
            height: 16px;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .error-border {
            border-color: var(--destructive) !important;
            border-width: 2px !important;
        }
    </style>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-background text-foreground min-h-screen">
    <div class="container mx-auto px-4 py-8 max-w-7xl">
        <!-- @COMPONENT: Header -->
        <header class="mb-8">
            <h1 class="text-3xl font-bold text-foreground flex items-center gap-3">
                <i class="fas fa-spider text-primary"></i>
                Price Scraper Dashboard
            </h1>
            <p class="text-muted-foreground mt-2">Monitor product prices across multiple websites</p>
        </header>
        <!-- @END_COMPONENT: Header -->

        <div class="grid grid-cols-1 xl:grid-cols-3 gap-8">
            <!-- @COMPONENT: AddScraperForm -->
            <div class="xl:col-span-1">
                <div class="bg-card border border-border rounded-lg p-6 shadow-sm">
                    <h2 class="text-xl font-semibold mb-6 flex items-center gap-2">
                        <i class="fas fa-plus text-primary"></i>
                        Add New Scraper
                    </h2>
                    
                    <form class="space-y-4" data-implementation="Handle form submission with validation">
                        <div>
                            <label for="itemName" class="block text-sm font-medium mb-2">Item Name</label>
                            <input 
                                type="text" 
                                id="itemName" 
                                data-bind="scraper.itemName"
                                placeholder="e.g., iPhone 15 Pro"
                                class="w-full px-3 py-2 bg-input border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                                data-mock="true"
                            />
                        </div>
                        
                        <div>
                            <label for="url" class="block text-sm font-medium mb-2">Target URL</label>
                            <input 
                                type="url" 
                                id="url" 
                                data-bind="scraper.url"
                                placeholder="https://example.com/product-page"
                                class="w-full px-3 py-2 bg-input border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                                data-mock="true"
                            />
                        </div>
                        
                        <div>
                            <label for="selector" class="block text-sm font-medium mb-2">Price Selector</label>
                            <input 
                                type="text" 
                                id="selector" 
                                data-bind="scraper.selector"
                                placeholder=".price, #cost, [data-price]"
                                class="w-full px-3 py-2 bg-input border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent font-mono text-sm"
                                data-mock="true"
                            />
                            <p class="text-xs text-muted-foreground mt-1">CSS selector for the price element</p>
                        </div>
                        
                        <button 
                            type="submit" 
                            data-event="click:handleAddScraper"
                            class="w-full bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors font-medium flex items-center justify-center gap-2"
                        >
                            <i class="fas fa-plus"></i>
                            Add Scraper
                        </button>
                    </form>
                </div>

                <!-- @COMPONENT: QuickActions -->
                <div class="bg-card border border-border rounded-lg p-6 shadow-sm mt-6">
                    <h3 class="text-lg font-semibold mb-4 flex items-center gap-2">
                        <i class="fas fa-bolt text-chart-3"></i>
                        Quick Actions
                    </h3>
                    
                    <div class="space-y-3">
                        <button 
                            data-event="click:handleUpdateAll"
                            class="w-full bg-secondary text-secondary-foreground px-4 py-2 rounded-md hover:bg-secondary/90 transition-colors font-medium flex items-center justify-center gap-2"
                        >
                            <i class="fas fa-sync-alt"></i>
                            Update All Scrapers
                        </button>
                        
                        <button 
                            data-event="click:handleExportData"
                            class="w-full bg-muted text-muted-foreground px-4 py-2 rounded-md hover:bg-muted/80 transition-colors font-medium flex items-center justify-center gap-2"
                        >
                            <i class="fas fa-download"></i>
                            Export Data
                        </button>
                    </div>
                </div>
                <!-- @END_COMPONENT: QuickActions -->
            </div>
            <!-- @END_COMPONENT: AddScraperForm -->

            <!-- @COMPONENT: ScraperTable -->
            <div class="xl:col-span-2">
                <div class="bg-card border border-border rounded-lg shadow-sm">
                    <div class="p-6 border-b border-border">
                        <div class="flex items-center justify-between">
                            <h2 class="text-xl font-semibold flex items-center gap-2">
                                <i class="fas fa-table text-primary"></i>
                                Active Scrapers
                                <span class="bg-muted text-muted-foreground px-2 py-1 rounded-full text-sm" data-bind="scrapers.length">5</span>
                            </h2>
                            <div class="flex items-center gap-2">
                                <span class="text-sm text-muted-foreground">Last updated:</span>
                                <span class="text-sm font-medium" data-bind="lastUpdated">2 minutes ago</span>
                            </div>
                        </div>
                    </div>

                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead class="border-b border-border">
                                <tr class="text-left">
                                    <th class="p-4 font-medium text-sm text-muted-foreground">Status</th>
                                    <th class="p-4 font-medium text-sm text-muted-foreground">Item</th>
                                    <th class="p-4 font-medium text-sm text-muted-foreground">Current Price</th>
                                    <th class="p-4 font-medium text-sm text-muted-foreground">Lowest Price</th>
                                    <th class="p-4 font-medium text-sm text-muted-foreground">Last Updated</th>
                                    <th class="p-4 font-medium text-sm text-muted-foreground">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- @MAP: scrapers.map(scraper => ( -->
                                <tr class="border-b border-border hover:bg-muted/50 transition-colors" data-mock="true">
                                    <td class="p-4">
                                        <span class="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-chart-2/20 text-chart-2">
                                            <i class="fas fa-check-circle"></i>
                                            Active
                                        </span>
                                    </td>
                                    <td class="p-4">
                                        <div>
                                            <div class="font-medium" data-bind="scraper.itemName">iPhone 15 Pro</div>
                                            <div class="text-sm text-muted-foreground truncate max-w-xs" data-bind="scraper.url">https://apple.com/iphone-15-pro</div>
                                        </div>
                                    </td>
                                    <td class="p-4">
                                        <span class="font-semibold text-lg" data-bind="scraper.currentPrice">$999</span>
                                    </td>
                                    <td class="p-4">
                                        <span class="font-medium text-chart-2" data-bind="scraper.lowestPrice">$949</span>
                                        <div class="text-xs text-muted-foreground">-$50 (5%)</div>
                                    </td>
                                    <td class="p-4">
                                        <span class="text-sm" data-bind="scraper.lastUpdated">2 min ago</span>
                                    </td>
                                    <td class="p-4">
                                        <div class="flex items-center gap-1">
                                            <button 
                                                data-event="click:handleUpdateScraper"
                                                class="p-2 text-muted-foreground hover:text-chart-3 hover:bg-muted rounded transition-colors"
                                                title="Update price"
                                            >
                                                <i class="fas fa-sync-alt"></i>
                                            </button>
                                            <button 
                                                data-event="click:handleEditScraper"
                                                class="p-2 text-muted-foreground hover:text-primary hover:bg-muted rounded transition-colors"
                                                title="Edit scraper"
                                            >
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button 
                                                data-event="click:handleDeleteScraper"
                                                class="p-2 text-muted-foreground hover:text-destructive hover:bg-muted rounded transition-colors"
                                                title="Delete scraper"
                                            >
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>

                                <tr class="border-b border-border hover:bg-muted/50 transition-colors" data-mock="true">
                                    <td class="p-4">
                                        <span class="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-chart-1/20 text-chart-1">
                                            <div class="spinner"></div>
                                            Updating
                                        </span>
                                    </td>
                                    <td class="p-4">
                                        <div>
                                            <div class="font-medium" data-bind="scraper.itemName">MacBook Pro 16"</div>
                                            <div class="text-sm text-muted-foreground truncate max-w-xs" data-bind="scraper.url">https://apple.com/macbook-pro-16</div>
                                        </div>
                                    </td>
                                    <td class="p-4">
                                        <span class="font-semibold text-lg" data-bind="scraper.currentPrice">$2,499</span>
                                    </td>
                                    <td class="p-4">
                                        <span class="font-medium text-chart-2" data-bind="scraper.lowestPrice">$2,299</span>
                                        <div class="text-xs text-muted-foreground">-$200 (8%)</div>
                                    </td>
                                    <td class="p-4">
                                        <span class="text-sm" data-bind="scraper.lastUpdated">Updating...</span>
                                    </td>
                                    <td class="p-4">
                                        <div class="flex items-center gap-1">
                                            <button 
                                                disabled
                                                class="p-2 text-muted-foreground/50 rounded cursor-not-allowed"
                                                title="Currently updating"
                                            >
                                                <i class="fas fa-sync-alt"></i>
                                            </button>
                                            <button 
                                                data-event="click:handleEditScraper"
                                                class="p-2 text-muted-foreground hover:text-primary hover:bg-muted rounded transition-colors"
                                                title="Edit scraper"
                                            >
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button 
                                                data-event="click:handleDeleteScraper"
                                                class="p-2 text-muted-foreground hover:text-destructive hover:bg-muted rounded transition-colors"
                                                title="Delete scraper"
                                            >
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>

                                <tr class="border-b border-border hover:bg-muted/50 transition-colors error-border" data-mock="true">
                                    <td class="p-4">
                                        <span class="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-destructive/20 text-destructive">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            Error
                                        </span>
                                    </td>
                                    <td class="p-4">
                                        <div>
                                            <div class="font-medium" data-bind="scraper.itemName">AirPods Pro</div>
                                            <div class="text-sm text-muted-foreground truncate max-w-xs" data-bind="scraper.url">https://example.com/invalid-page</div>
                                        </div>
                                    </td>
                                    <td class="p-4">
                                        <span class="text-destructive font-medium">Error</span>
                                    </td>
                                    <td class="p-4">
                                        <span class="font-medium text-muted-foreground" data-bind="scraper.lowestPrice">$199</span>
                                    </td>
                                    <td class="p-4">
                                        <span class="text-sm text-destructive" data-bind="scraper.lastUpdated">Failed 5m ago</span>
                                    </td>
                                    <td class="p-4">
                                        <div class="flex items-center gap-1">
                                            <button 
                                                data-event="click:handleUpdateScraper"
                                                class="p-2 text-muted-foreground hover:text-chart-3 hover:bg-muted rounded transition-colors"
                                                title="Retry update"
                                            >
                                                <i class="fas fa-redo"></i>
                                            </button>
                                            <button 
                                                data-event="click:handleEditScraper"
                                                class="p-2 text-muted-foreground hover:text-primary hover:bg-muted rounded transition-colors"
                                                title="Edit scraper"
                                            >
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button 
                                                data-event="click:handleDeleteScraper"
                                                class="p-2 text-muted-foreground hover:text-destructive hover:bg-muted rounded transition-colors"
                                                title="Delete scraper"
                                            >
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>

                                <tr class="border-b border-border hover:bg-muted/50 transition-colors" data-mock="true">
                                    <td class="p-4">
                                        <span class="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-chart-2/20 text-chart-2">
                                            <i class="fas fa-check-circle"></i>
                                            Active
                                        </span>
                                    </td>
                                    <td class="p-4">
                                        <div>
                                            <div class="font-medium" data-bind="scraper.itemName">Samsung Galaxy S24</div>
                                            <div class="text-sm text-muted-foreground truncate max-w-xs" data-bind="scraper.url">https://samsung.com/galaxy-s24</div>
                                        </div>
                                    </td>
                                    <td class="p-4">
                                        <span class="font-semibold text-lg" data-bind="scraper.currentPrice">$799</span>
                                    </td>
                                    <td class="p-4">
                                        <span class="font-medium text-chart-2" data-bind="scraper.lowestPrice">$749</span>
                                        <div class="text-xs text-muted-foreground">-$50 (6%)</div>
                                    </td>
                                    <td class="p-4">
                                        <span class="text-sm" data-bind="scraper.lastUpdated">15 min ago</span>
                                    </td>
                                    <td class="p-4">
                                        <div class="flex items-center gap-1">
                                            <button 
                                                data-event="click:handleUpdateScraper"
                                                class="p-2 text-muted-foreground hover:text-chart-3 hover:bg-muted rounded transition-colors"
                                                title="Update price"
                                            >
                                                <i class="fas fa-sync-alt"></i>
                                            </button>
                                            <button 
                                                data-event="click:handleEditScraper"
                                                class="p-2 text-muted-foreground hover:text-primary hover:bg-muted rounded transition-colors"
                                                title="Edit scraper"
                                            >
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button 
                                                data-event="click:handleDeleteScraper"
                                                class="p-2 text-muted-foreground hover:text-destructive hover:bg-muted rounded transition-colors"
                                                title="Delete scraper"
                                            >
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>

                                <tr class="border-b border-border hover:bg-muted/50 transition-colors" data-mock="true">
                                    <td class="p-4">
                                        <span class="inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium bg-chart-2/20 text-chart-2">
                                            <i class="fas fa-check-circle"></i>
                                            Active
                                        </span>
                                    </td>
                                    <td class="p-4">
                                        <div>
                                            <div class="font-medium" data-bind="scraper.itemName">Dell XPS 13</div>
                                            <div class="text-sm text-muted-foreground truncate max-w-xs" data-bind="scraper.url">https://dell.com/xps-13</div>
                                        </div>
                                    </td>
                                    <td class="p-4">
                                        <span class="font-semibold text-lg" data-bind="scraper.currentPrice">$1,299</span>
                                    </td>
                                    <td class="p-4">
                                        <span class="font-medium text-chart-2" data-bind="scraper.lowestPrice">$1,199</span>
                                        <div class="text-xs text-muted-foreground">-$100 (8%)</div>
                                    </td>
                                    <td class="p-4">
                                        <span class="text-sm" data-bind="scraper.lastUpdated">1 hour ago</span>
                                    </td>
                                    <td class="p-4">
                                        <div class="flex items-center gap-1">
                                            <button 
                                                data-event="click:handleUpdateScraper"
                                                class="p-2 text-muted-foreground hover:text-chart-3 hover:bg-muted rounded transition-colors"
                                                title="Update price"
                                            >
                                                <i class="fas fa-sync-alt"></i>
                                            </button>
                                            <button 
                                                data-event="click:handleEditScraper"
                                                class="p-2 text-muted-foreground hover:text-primary hover:bg-muted rounded transition-colors"
                                                title="Edit scraper"
                                            >
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button 
                                                data-event="click:handleDeleteScraper"
                                                class="p-2 text-muted-foreground hover:text-destructive hover:bg-muted rounded transition-colors"
                                                title="Delete scraper"
                                            >
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                <!-- @END_MAP )) -->
                            </tbody>
                        </table>
                    </div>

                    <div class="p-4 border-t border-border bg-muted/30">
                        <div class="flex items-center justify-between text-sm text-muted-foreground">
                            <span>Showing 5 scrapers</span>
                            <div class="flex items-center gap-4">
                                <span>Auto-refresh: <span class="text-chart-2 font-medium">ON</span></span>
                                <span>Next update in: <span class="font-medium" data-bind="nextUpdate">4:32</span></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- @END_COMPONENT: ScraperTable -->
        </div>

        <!-- @COMPONENT: EditModal (Hidden by default) -->
        <div id="editModal" class="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 hidden" data-implementation="Modal overlay for editing scrapers">
            <div class="fixed inset-0 flex items-center justify-center p-4">
                <div class="bg-card border border-border rounded-lg shadow-lg max-w-md w-full p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold">Edit Scraper</h3>
                        <button class="text-muted-foreground hover:text-foreground">
                            <i class="fas fa-times"></i>
                        </button>
                    </div>
                    
                    <form class="space-y-4" data-implementation="Handle edit form submission">
                        <div>
                            <label class="block text-sm font-medium mb-2">Item Name</label>
                            <input 
                                type="text" 
                                value="iPhone 15 Pro"
                                class="w-full px-3 py-2 bg-input border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                                data-mock="true"
                            />
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium mb-2">Target URL</label>
                            <input 
                                type="url" 
                                value="https://apple.com/iphone-15-pro"
                                class="w-full px-3 py-2 bg-input border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent"
                                data-mock="true"
                            />
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium mb-2">Price Selector</label>
                            <input 
                                type="text" 
                                value=".price"
                                class="w-full px-3 py-2 bg-input border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent font-mono text-sm"
                                data-mock="true"
                            />
                        </div>
                        
                        <div class="flex gap-3 pt-4">
                            <button 
                                type="submit"
                                class="flex-1 bg-primary text-primary-foreground px-4 py-2 rounded-md hover:bg-primary/90 transition-colors font-medium"
                            >
                                Save Changes
                            </button>
                            <button 
                                type="button"
                                class="flex-1 bg-secondary text-secondary-foreground px-4 py-2 rounded-md hover:bg-secondary/90 transition-colors font-medium"
                            >
                                Cancel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        <!-- @END_COMPONENT: EditModal -->
    </div>

    <script>
        // TODO: Implement actual React components and API integration
        // This is a static mockup for demonstration purposes
        
        // Simulated functionality for demonstration
        (function() {
            // Auto-refresh countdown simulation
            let timeLeft = 272; // 4:32 in seconds
            const countdownElement = document.querySelector('[data-bind="nextUpdate"]');
            
            function updateCountdown() {
                const minutes = Math.floor(timeLeft / 60);
                const seconds = timeLeft % 60;
                if (countdownElement) {
                    countdownElement.textContent = `${minutes}:${seconds.toString().padStart(2, '0')}`;
                }
                
                timeLeft--;
                if (timeLeft < 0) {
                    timeLeft = 300; // Reset to 5 minutes
                }
            }
            
            setInterval(updateCountdown, 1000);
            
            // Modal toggle functionality
            const editModal = document.getElementById('editModal');
            const editButtons = document.querySelectorAll('[data-event*="handleEditScraper"]');
            
            editButtons.forEach(button => {
                button.addEventListener('click', (e) => {
                    e.preventDefault();
                    if (editModal) {
                        editModal.classList.remove('hidden');
                    }
                });
            });
            
            // Close modal
            const closeButton = editModal?.querySelector('.fa-times')?.parentElement;
            const cancelButton = editModal?.querySelector('button[type="button"]');
            
            [closeButton, cancelButton].forEach(button => {
                if (button) {
                    button.addEventListener('click', (e) => {
                        e.preventDefault();
                        if (editModal) {
                            editModal.classList.add('hidden');
                        }
                    });
                }
            });
            
            // Close modal on backdrop click
            if (editModal) {
                editModal.addEventListener('click', (e) => {
                    if (e.target === editModal) {
                        editModal.classList.add('hidden');
                    }
                });
            }
        })();
    </script>
</body>
</html>