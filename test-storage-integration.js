// Test the storage integration with average price calculation
import { storage } from './dist/index.js';

async function testStorageIntegration() {
  console.log('=== Testing Storage Integration with Average Price ===\n');

  try {
    // Test with the existing ThinkPad scraper
    const thinkpadId = '1f417294-782a-47d1-a7ea-e63cf7fbd300';
    
    console.log('1. Testing getScraper method...');
    const scraper = await storage.getScraper(thinkpadId);
    
    if (scraper) {
      console.log('Scraper found:');
      console.log(`  Item: ${scraper.itemName.substring(0, 50)}...`);
      console.log(`  Current Price: ${scraper.currentPrice}`);
      console.log(`  Lowest Price: ${scraper.lowestPrice}`);
      console.log(`  Previous Price: ${scraper.previousPrice}`);
      console.log(`  Average Price: ${scraper.averagePrice}`);
      
      if (scraper.averagePrice !== null) {
        console.log('✓ Average price is calculated correctly!');
      } else {
        console.log('✗ Average price is still null');
      }
    } else {
      console.log('✗ Scraper not found');
    }

    console.log('\n2. Testing getAllScrapers method...');
    const allScrapers = await storage.getAllScrapers();
    console.log(`Found ${allScrapers.length} scrapers`);
    
    allScrapers.forEach((scraper, index) => {
      console.log(`  ${index + 1}. ${scraper.itemName.substring(0, 30)}...`);
      console.log(`     Average Price: ${scraper.averagePrice}`);
    });

    console.log('\n3. Testing price history...');
    const history = await storage.getPriceHistory(thinkpadId);
    console.log(`Found ${history.length} price history entries for ThinkPad:`);
    history.forEach((entry, index) => {
      console.log(`  ${index + 1}. ${entry.price} at ${entry.timestamp}`);
    });

    // Calculate expected average manually
    if (history.length > 0) {
      const prices = history.map(h => parseFloat(h.price));
      const expectedAverage = (prices.reduce((sum, price) => sum + price, 0) / prices.length).toFixed(2);
      console.log(`\nExpected average: ${expectedAverage}`);
      console.log(`Actual average: ${scraper?.averagePrice}`);
      console.log(`Match: ${expectedAverage === scraper?.averagePrice ? 'YES' : 'NO'}`);
    }

    console.log('\n=== Test Complete ===');

  } catch (error) {
    console.error('Error during test:', error);
  }
}

testStorageIntegration();
